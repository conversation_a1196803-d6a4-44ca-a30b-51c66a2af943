import os
from dotenv import load_dotenv
from typing import Optional


load_dotenv()

# N1 API Authentication
N1_API_HEADER_NAME = os.getenv("N1_API_HEADER_NAME", "N1-Api-Key")
N1_API_KEY = os.getenv("N1_API_KEY", "n1-devkey-Vl2R3he2isa9")
USERNAME = os.getenv("N1_USERNAME", "n1admin")
PASSWORD = os.getenv("N1_PASSWORD", "n1_secret+12")
N1_API_BASE_URL = os.getenv("N1_API_BASE_URL", "https://n1-api-dev-940864807841.us-central1.run.app")
N1_API_RECORD_STATUS_URL = os.getenv("N1_API_RECORD_STATUS_URL", f"{N1_API_BASE_URL}/records/status")
N1_API_SYNC_BIOMARKERS_URL = os.getenv("N1_API_SYNC_BIOMARKERS_URL", f"{N1_API_BASE_URL}/records/sync-biomarkers")

# Configuration
BUBBLE_API_BASE_URL: Optional[str] = os.getenv("BUBBLE_API_BASE_URL")
BUBBLE_API_AUTH_KEY: Optional[str] = os.getenv("BUBBLE_API_AUTH_KEY")
BUBBLE_BIOMARKER_SYNC_URL: str = f'{BUBBLE_API_BASE_URL}/{os.getenv("BUBBLE_BIOMARKER_SYNC_ENDPOINT")}'
BUBBLE_DIAGNOSIS_SYNC_URL: str = f'{BUBBLE_API_BASE_URL}/{os.getenv("BUBBLE_DIAGNOSIS_SYNC_ENDPOINT")}'
BUBBLE_PROCEDURE_SYNC_URL: str = f'{BUBBLE_API_BASE_URL}/{os.getenv("BUBBLE_PROCEDURE_SYNC_ENDPOINT")}'
BUBBLE_RECORD_STATUS_URL: str = f'{BUBBLE_API_BASE_URL}/{os.getenv("BUBBLE_RECORD_STATUS_ENDPOINT", "wf/receive_record_status")}'
BUBBLE_REPORT_STATUS_URL: str = f'{BUBBLE_API_BASE_URL}/{os.getenv("BUBBLE_REPORT_STATUS_ENDPOINT", "wf/receive_report_status")}'
SKIP_BUBBLE_UPDATES = os.getenv("SKIP_BUBBLE_UPDATES", "").strip().lower() in {"true", "1", "yes"}

# Chat configuration
CHR_CHAT_URL = os.getenv("CHR_CHAT_URL", "http://0.0.0.0:8082/chat/request")
CHR_CHAT_INIT_URL = os.getenv("CHR_CHAT_INIT_URL", "http://0.0.0.0:8082/data/process-chr")
# DATABASE_URL: str =  "sqlite:///./test.db"
# DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./test.db")
DEV_BUCKET_NAME = os.getenv("DEV_BUCKET_NAME", "")
GCP_SERVICE_JSON = os.getenv("GCP_SERVICE_JSON", "")
PROJECT_ID = os.getenv("PROJECT_ID")
REGION = os.getenv("REGION", "us-central1")
SEQUENTIAL_REPORT_GENERATION_JOB_NAME = os.getenv("SEQUENTIAL_REPORT_GENERATION_JOB_NAME", "sequential-report-generator")
TEMPLATE_REPORT_GENERATION_JOB_NAME = os.getenv("TEMPLATE_REPORT_GENERATION_JOB_NAME", "workflow-generative-template")
BIOMARKERS_TABLE_NAME = os.getenv("BIOMARKERS_TABLE_NAME", "user_biomarkers")
RECORDS_TABLE_NAME = os.getenv("RECORDS_TABLE_NAME", "record_requests")
FILE_NAME_COLUMN = os.getenv("FILE_NAME_COLUMN", "file_name")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "sk-wtdCx6c9AtodTge_00j7Mw")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://n1-litellm.deriv.ai/v1")

ROUTER_MODEL = os.getenv("ROUTER_MODEL", "gpt-4o-mini")
EXA_API_KEY = os.getenv("EXA_API_KEY")
FIRE_CRAWL_API_KEY = os.getenv("FIRE_CRAWL_API_KEY")
QDRANT_API_URL = os.getenv("QDRANT_API_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

MODEL = os.getenv("MODEL", "claude-3.7-sonnet")
DB_USERNAME = os.getenv("DB_USERNAME")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_HOST = os.getenv("DB_HOST", "127.0.0.1")
DB_HOST_PUBLIC = os.getenv("DB_HOST_PUBLIC", "*************")
DATABASE_NAME = os.getenv("DATABASE_NAME")
DATABASE_URL = os.getenv("DATABASE_URL", None)
POSTPROCESSING_MODEL = os.getenv("POSTPROCESSING_MODEL", "gpt-4o-mini")

if DATABASE_URL is None:
    DATABASE_URL=f"postgresql+psycopg2://{DB_USERNAME}:{DB_PASSWORD}@{DB_HOST}/{DATABASE_NAME}"

GCP_DATA_PROCESSING_PARSERS={
    "Sequential" : {
        "region": "europe-west1",
        "job_name": "data-processing-service-pt"
    },
    "Langroid": {
        "region": "us-south1",
        "job_name": "data-processing-langroid-pt"
    }
}
AWS_DATA_PROCESSING_PARSERS={
    "Sequential" : {
        "job_queue": "n1DataProcessQueue",
        "job_definition": "data-processing-service",
        "region": "us-east-2"
    }
}
AWS_ACCESS_KEY=os.getenv("AWS_ACCESS_KEY")
AWS_SECRET_ACCESS_KEY=os.getenv("AWS_SECRET_ACCESS_KEY")

AccessKey="********************"
SecretAccessKey="o3kxfslkts41ePqIlPJE91TspmhrBxkAdt43bBST"
# Validate required environment variables
def validate_config():
    missing_vars = []

    if not N1_API_KEY or N1_API_KEY == "default-dev-api-key":
        missing_vars.append("N1_API_KEY")

    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
