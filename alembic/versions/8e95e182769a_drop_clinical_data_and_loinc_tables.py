"""drop_clinical_data_and_loinc_tables

Revision ID: 8e95e182769a
Revises: 04d6226e96ac
Create Date: 2025-07-23 17:08:35.330869

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import models



# revision identifiers, used by Alembic.
revision: str = '8e95e182769a'
down_revision: Union[str, None] = '04d6226e96ac'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Drop clinical_data and loinc_records tables."""
    # Drop clinical_data table
    op.drop_table('clinical_data')

    # Drop loinc_records table
    op.drop_table('loinc_records')


def downgrade() -> None:
    """Recreate clinical_data and loinc_records tables."""
    # Note: This is a destructive migration. The downgrade recreates empty tables
    # but does not restore data. Consider backing up data before running this migration.

    # Recreate loinc_records table
    op.create_table('loinc_records',
        sa.Column('loinc_num', sa.String(), nullable=False),
        sa.Column('component', sa.String(), nullable=True),
        sa.Column('property', sa.String(), nullable=True),
        sa.Column('time_aspct', sa.String(), nullable=True),
        sa.Column('system', sa.String(), nullable=True),
        sa.Column('scale_typ', sa.String(), nullable=True),
        sa.Column('method_typ', sa.String(), nullable=True),
        sa.Column('class_', sa.String(), nullable=True),
        sa.Column('classtype', sa.Integer(), nullable=True),
        sa.Column('long_common_name', sa.String(), nullable=True),
        sa.Column('shortname', sa.String(), nullable=True),
        sa.Column('external_copyright_notice', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('version_first_released', sa.String(), nullable=True),
        sa.Column('version_last_changed', sa.String(), nullable=True),
        sa.Column('example_units', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('loinc_num')
    )

    # Recreate clinical_data table
    op.create_table('clinical_data',
        sa.Column('id', models.GUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=models.UtcNow(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=models.UtcNow(), nullable=True),
        sa.Column('user_id', models.GUID(), nullable=True),
        sa.Column('record_id', sa.String(), nullable=False),
        sa.Column('day', sa.Integer(), nullable=True),
        sa.Column('year', sa.Integer(), nullable=True),
        sa.Column('month', sa.Integer(), nullable=True),
        sa.Column('test_name', sa.String(), nullable=True),
        sa.Column('result', sa.String(), nullable=True),
        sa.Column('result_numeric', sa.Float(), nullable=True),
        sa.Column('reference_range', sa.String(), nullable=True),
        sa.Column('reference_range_min', sa.Float(), nullable=True),
        sa.Column('reference_range_max', sa.Float(), nullable=True),
        sa.Column('out_of_range', sa.Boolean(), nullable=True),
        sa.Column('unit', sa.String(), nullable=True),
        sa.Column('filename', sa.String(), nullable=True),
        sa.Column('sample_source', sa.String(), nullable=True),
        sa.Column('method', sa.String(), nullable=True),
        sa.Column('context', sa.Text(), nullable=True),
        sa.Column('loinc_standard_name', sa.String(), nullable=True),
        sa.Column('loinc_code', sa.String(), nullable=True),
        sa.Column('health_areas', models.ArrayOfText(), nullable=True),
        sa.Column('edited', sa.Boolean(), nullable=True),
        sa.Column('excluded', sa.Boolean(), nullable=True),
        sa.Column('additional_data', models.JSONVariant(), nullable=True),
        sa.Column('page_number', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['record_id'], ['record_requests.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Recreate indexes
    op.create_index(op.f('ix_clinical_data_additional_data'), 'clinical_data', ['additional_data'], unique=False)
    op.create_index(op.f('ix_clinical_data_record_id'), 'clinical_data', ['record_id'], unique=False)
    op.create_index(op.f('ix_clinical_data_user_id'), 'clinical_data', ['user_id'], unique=False)
