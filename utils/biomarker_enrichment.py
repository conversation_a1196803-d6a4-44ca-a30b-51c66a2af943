import sys
import os
import json
import re
from openai import OpenAI
from typing import List, Dict, Optional, Any
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON>emplate, PromptTemplate
from pydantic import BaseModel, Field
from langchain_core.output_parsers import JsonOutputParser
from fastapi import HTTPException, BackgroundTasks
import warnings

# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import POSTPROCESSING_MODEL, OPENAI_API_KEY, OPENAI_BASE_URL
from models import (
    UserRecordRequestORM,
    UserReportGenRequestORM,
    UserBiomarkerORM,
    CanonicalRecordORM
)

from database import get_db_session

# Suppress Pydantic serialization warnings
warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")


class DataKeys:
    # Input keys
    N1_ID = "n1_id"
    LOINC_STANDARD_NAME = "loinc_standard_name"
    LOINC_SHORT_NAME = "loinc_short_name"
    CANONICAL_NAME = "canonical_name"
    SAMPLE_SOURCE = "sample_source"
    METHOD = "method"
    TEST_RESULT = "test_result"
    UNIT = "unit"
    TEST_DATE = "test_date"
    CUSTOM_REFERENCE_RANGE = "custom_reference_range"
    TYPE = "type"
    TEST_NAME = "test_name"
    CONTEXT = "context"
    HEALTH_AREAS = "health_areas"
    HEALTH_AREA_COUNT = "health_area_count"

    # Output keys (added by enrichment)
    IS_NUMERIC = "is_numeric"
    MIN_REFERENCE_RANGE = "min_reference_range"
    MAX_REFERENCE_RANGE = "max_reference_range"
    OUT_OF_RANGE = "out_of_range"
    STANDARDIZED_UNITS = "standardized_units"
    RESULT_STANDARDIZED = "result_standardized"
    STANDARDIZED_MIN_REFERENCE_RANGE = "standardized_min_reference_range"
    STANDARDIZED_MAX_REFERENCE_RANGE = "standardized_max_reference_range"

class PostProcess:
    client = OpenAI(api_key=OPENAI_API_KEY, base_url=OPENAI_BASE_URL)

    @staticmethod
    def enrich_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich the data with additional information:
        - standardized units
        - is numeric value flag
        - is out of range flag

        Args:
            data (Dict[str, any]): The data to enrich

        Returns:
            Dict[str, any]: The enriched data
        """
        try:
            # Example enrichment logic
            row = data
            row = PostProcess.get_is_numeric_value(row)

            if row.get(DataKeys.IS_NUMERIC, True):
                row = PostProcess.get_min_max_values(row)
                row = PostProcess.is_out_of_range_flag(row)
                row = PostProcess.standardize_units(row)
                row = PostProcess.standardize_reference_range(row)
            else:
                # If not numeric, set min/max to None
                row[DataKeys.MIN_REFERENCE_RANGE] = None
                row[DataKeys.MAX_REFERENCE_RANGE] = None
                row[DataKeys.STANDARDIZED_MIN_REFERENCE_RANGE] = None
                row[DataKeys.STANDARDIZED_MAX_REFERENCE_RANGE] = None
                row[DataKeys.OUT_OF_RANGE] = None
                row[DataKeys.STANDARDIZED_UNITS] = row.get(DataKeys.UNIT, None)
                row[DataKeys.RESULT_STANDARDIZED] = row.get(DataKeys.TEST_RESULT, None)

            return data
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Error during data enrichment: {str(e)}"
            )

    @staticmethod
    def get_is_numeric_value(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adds 'is_numeric' field to the biomarker JSON based on whether test_result is a float.

        Args:
            data (dict): The biomarker dictionary

        Returns:
            dict: The modified dictionary with 'is_numeric' field added
        """
        try:
            # Try to convert test_result to float
            float(data.get(DataKeys.TEST_RESULT))
            data[DataKeys.IS_NUMERIC] = True
        except (ValueError, TypeError):
            # If conversion fails, it's not numeric
            data[DataKeys.IS_NUMERIC] = False

        return data

    @staticmethod
    def get_min_max_values(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adds 'min_value' and 'max_value' fields to the biomarker JSON based on the reference_range.

        Args:
            data (dict): The biomarker dictionary

        Returns:
            dict: The modified dictionary with 'min_value' and 'max_value' fields added
        """
        # Initialize min_value and max_value to None
        data[DataKeys.MIN_REFERENCE_RANGE] = None
        data[DataKeys.MAX_REFERENCE_RANGE] = None

        ref_range = data.get(DataKeys.CUSTOM_REFERENCE_RANGE, "")

        if not ref_range or not isinstance(ref_range, str):
            print("No valid reference range provided, skipping")
            return data

        ref_range = PostProcess._extract_range_pattern(ref_range)

        try:
            # Handle range with dash (e.g., "10-20", "10 - 20")
            if "-" in ref_range and not ref_range.startswith("-"):
                # Split on dash and handle multiple dashes by taking first two parts
                parts = ref_range.split("-", 1)
                if len(parts) == 2:
                    min_str, max_str = parts[0].strip(), parts[1].strip()
                    if min_str and max_str:  # Both parts must be non-empty
                        data[DataKeys.MIN_REFERENCE_RANGE] = float(min_str)
                        data[DataKeys.MAX_REFERENCE_RANGE] = float(max_str)

            # Handle less than (e.g., "<10", "< 10")
            elif "<" in ref_range:
                if ref_range.startswith("<="):
                    max_value = ref_range[2:].strip()
                else:
                    max_value = ref_range.replace("<", "").strip()

                if max_value:
                    data[DataKeys.MIN_REFERENCE_RANGE] = None
                    data[DataKeys.MAX_REFERENCE_RANGE] = float(max_value)

            # Handle greater than (e.g., ">5", "> 5")
            elif ">" in ref_range:
                if ref_range.startswith(">="):
                    min_value = ref_range[2:].strip()
                else:
                    min_value = ref_range.replace(">", "").strip()

                if min_value:
                    data[DataKeys.MIN_REFERENCE_RANGE] = float(min_value)
                    data[DataKeys.MAX_REFERENCE_RANGE] = None
        except (ValueError, AttributeError, IndexError) as e:
            # If any conversion fails, keep min_value and max_value as None
            data[DataKeys.MIN_REFERENCE_RANGE] = None
            data[DataKeys.MAX_REFERENCE_RANGE] = None

        return data

    @staticmethod
    def is_out_of_range_flag(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adds 'is_out_of_range' field to the biomarker JSON based on whether
        the test_result falls outside the reference range.

        Args:
            data (dict): The biomarker dictionary with test_result, min_value, and max_value

        Returns:
            dict: The modified dictionary with 'is_out_of_range' field added
        """
        # Initialize is_out_of_range to False

        data[DataKeys.OUT_OF_RANGE] = "no"

        # Get the test result
        test_result = data.get(DataKeys.TEST_RESULT)
        min_value = data.get(DataKeys.MIN_REFERENCE_RANGE)
        max_value = data.get(DataKeys.MAX_REFERENCE_RANGE)

        # If test_result is not available or not numeric, can't determine range
        if test_result is None:
            return data

        try:
            # Convert test_result to float for comparison
            test_value = float(test_result)

            # Check if out of range based on available min/max values
            is_out_of_range = "no"

            # Check lower bound
            if min_value is not None and test_value < min_value:
                is_out_of_range = "yes"

            # Check upper bound
            if max_value is not None and test_value > max_value:
                is_out_of_range = "yes"

            data[DataKeys.OUT_OF_RANGE] = is_out_of_range

        except (ValueError, TypeError):
            # If conversion fails, keep is_out_of_range as False
            data[DataKeys.OUT_OF_RANGE] = "no"


        return data

    @staticmethod
    def standardize_units(data: Dict[str, Any]) -> Dict[str, Any]:
        data[DataKeys.STANDARDIZED_UNITS] = data.get(DataKeys.UNIT, "")
        try:
            data[DataKeys.RESULT_STANDARDIZED] = float(data[DataKeys.TEST_RESULT])
        except (ValueError, TypeError):
            data[DataKeys.RESULT_STANDARDIZED] = None

        # Canonical logic
        try:
            with get_db_session(commit=False) as db:
                canonical_orm = (
                    db.query(CanonicalRecordORM)
                    .filter(CanonicalRecordORM.canonical_name == data.get(DataKeys.CANONICAL_NAME))
                    .first()
                )
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Error fetching canonical record: {str(e)}"
            )

        example_units = canonical_orm.standard_unit if canonical_orm else None

        current_units = data.get(DataKeys.UNIT, "")
        print(f"Current units: {current_units}, Example units: {example_units}")
        result = data.get(DataKeys.TEST_RESULT, [])

        if current_units and example_units:
            # Normalize both units to lowercase for comparison
            # current_units = current_units.lower()
            # example_units = example_units.lower()
            example_units_list = [unit.strip() for unit in example_units.split(";")]

            if current_units not in example_units_list:
                example_units = example_units_list[0]  # Use the first example unit
                dimensions_equal = PostProcess._is_dimensionally_equivalent(example_units, current_units) 
                if dimensions_equal:
                    print(f"Units are dimensionally equivalent: {current_units} and {example_units}")
                    print(f"Standardizing units: {current_units} to {example_units}")
                    parsed_results = PostProcess._unit_conversion(
                        current_units, example_units, result
                    )
                    data[DataKeys.STANDARDIZED_UNITS] = example_units
                    data[DataKeys.RESULT_STANDARDIZED] = float(parsed_results)


        return data
    
    def _is_dimensionally_equivalent(example_units: str, current_units: str) -> bool:
        """
        Check if two units are dimensionally equivalent.
        Returns False if example_units is '%' and current_units is not '%', otherwise True.

        Args:
            example_units (str): The example unit of measurement.
            current_units (str): The current unit of measurement.

        Returns:
            bool: True if the units are dimensionally equivalent, False otherwise.
        """
        # If example_units is '%' and current_units is not '%', they are not compatible
        example_units = example_units.strip()
        current_units = current_units.strip()
        if example_units == '%' and current_units != '%':
            return False
        elif current_units == '%' and example_units != '%':
            return False
        
        # All other cases are considered compatible
        return True

    def _extract_range_pattern(value_str: str) -> str:
        """
        Extract the numeric range pattern from a string that may contain units.
        Examples:
            "15-20mg" -> "15-20"
            ">5g/dl" -> ">5"
            "<10ml" -> "<10"
            ">=15.5mg/dl" -> ">=15.5"
            "10 - 20 g" -> "10 - 20"
            "5.5mg" -> "5.5"

        Args:
            value_str (str): Input string containing numeric range with potential units

        Returns:
            str: Extracted numeric pattern without units

        Raises:
            ValueError: If no numeric pattern is found
        """
        # Clean the input string
        cleaned_str = value_str.strip()

        # Regex patterns for different range formats
        patterns = [
            # Range with dash: "15-20mg", "10.5 - 20.5 g/dl"
            r"(\d+\.?\d*\s*-\s*\d+\.?\d*)",
            # Range with space: "10.5 20.5", "15 20"
            r"(\d+\.?\d*\s+\d+\.?\d*)",
            # Greater than or equal: ">=15mg", ">= 15.5 g/dl"
            r"(>=\s*\d+\.?\d*)",
            # Less than or equal: "<=10ml", "<= 10.5 g/dl"
            r"(<=\s*\d+\.?\d*)",
            # Greater than: ">5mg", "> 5.5 g/dl"
            r"(>\s*\d+\.?\d*)",
            # Less than: "<10ml", "< 10.5 g/dl"
            r"(<\s*\d+\.?\d*)",
        ]

        # Try each pattern in order of specificity
        for pattern in patterns:
            match = re.search(pattern, cleaned_str)
            if match:
                # Clean up extra spaces in the matched result
                result = re.sub(r"\s+", " ", match.group(1).strip())
                # Remove spaces around operators for consistency
                result = re.sub(r"\s*([<>=\-])\s*", r"\1", result)
                return result

        # If no pattern matches, return the cleaned string as is
        return cleaned_str

    def _extract_range_pattern(value_str: str) -> str:
        """
        Extract the numeric range pattern from a string that may contain units.
        Examples:
            "15-20mg" -> "15-20"
            ">5g/dl" -> ">5"
            "<10ml" -> "<10"
            ">=15.5mg/dl" -> ">=15.5"
            "10 - 20 g" -> "10 - 20"
            "5.5mg" -> "5.5"

        Args:
            value_str (str): Input string containing numeric range with potential units

        Returns:
            str: Extracted numeric pattern without units

        Raises:
            ValueError: If no numeric pattern is found
        """
        # Clean the input string
        cleaned_str = value_str.strip()

        # Regex patterns for different range formats
        patterns = [
            # Range with dash: "15-20mg", "10.5 - 20.5 g/dl"
            r"(\d+\.?\d*\s*-\s*\d+\.?\d*)",
            # Range with space: "10.5 20.5", "15 20"
            r"(\d+\.?\d*\s+\d+\.?\d*)",
            # Greater than or equal: ">=15mg", ">= 15.5 g/dl"
            r"(>=\s*\d+\.?\d*)",
            # Less than or equal: "<=10ml", "<= 10.5 g/dl"
            r"(<=\s*\d+\.?\d*)",
            # Greater than: ">5mg", "> 5.5 g/dl"
            r"(>\s*\d+\.?\d*)",
            # Less than: "<10ml", "< 10.5 g/dl"
            r"(<\s*\d+\.?\d*)",
        ]

        # Try each pattern in order of specificity
        for pattern in patterns:
            match = re.search(pattern, cleaned_str)
            if match:
                # Clean up extra spaces in the matched result
                result = re.sub(r"\s+", " ", match.group(1).strip())
                # Remove spaces around operators for consistency
                result = re.sub(r"\s*([<>=\-])\s*", r"\1", result)
                return result

        # If no pattern matches, return the cleaned string as is
        return cleaned_str

    def _extract_range_pattern(value_str: str) -> str:
        """
        Extract the numeric range pattern from a string that may contain units.
        Examples:
            "15-20mg" -> "15-20"
            ">5g/dl" -> ">5"
            "<10ml" -> "<10"
            ">=15.5mg/dl" -> ">=15.5"
            "10 - 20 g" -> "10 - 20"
            "5.5mg" -> "5.5"

        Args:
            value_str (str): Input string containing numeric range with potential units

        Returns:
            str: Extracted numeric pattern without units

        Raises:
            ValueError: If no numeric pattern is found
        """
        # Clean the input string
        cleaned_str = value_str.strip()

        # Regex patterns for different range formats
        patterns = [
            # Range with dash: "15-20mg", "10.5 - 20.5 g/dl"
            r"(\d+\.?\d*\s*-\s*\d+\.?\d*)",
            # Range with space: "10.5 20.5", "15 20"
            r"(\d+\.?\d*\s+\d+\.?\d*)",
            # Greater than or equal: ">=15mg", ">= 15.5 g/dl"
            r"(>=\s*\d+\.?\d*)",
            # Less than or equal: "<=10ml", "<= 10.5 g/dl"
            r"(<=\s*\d+\.?\d*)",
            # Greater than: ">5mg", "> 5.5 g/dl"
            r"(>\s*\d+\.?\d*)",
            # Less than: "<10ml", "< 10.5 g/dl"
            r"(<\s*\d+\.?\d*)",
        ]

        # Try each pattern in order of specificity
        for pattern in patterns:
            match = re.search(pattern, cleaned_str)
            if match:
                # Clean up extra spaces in the matched result
                result = re.sub(r"\s+", " ", match.group(1).strip())
                # Remove spaces around operators for consistency
                result = re.sub(r"\s*([<>=\-])\s*", r"\1", result)
                return result

        # If no pattern matches, return the cleaned string as is
        return cleaned_str

    # Define your desired data structure.
    class Converter(BaseModel):
        result: float = Field(
            description="answer to the value after the conversion of units"
        )

    @staticmethod
    def _unit_conversion(source_unit: str, destination_unit: str, value: str) -> float:
        """
        Convert a value from source units to destination units using an LLM.

        Args:
            source_unit (str): The source unit of measurement.
            destination_unit (str): The destination unit of measurement.
            value (str): The value to convert.

        Returns:
            float: The converted value in the destination units.
        """
        SYSTEM_PROMPT = """
                    You are a helpful AI bot. You are responsible for converting from 
                    one unit to another. you will be provided with a source units, 
                    a destination unit, and a value (which will be in source units).
                    You will return the converted value in the destination units.
                    """
        USER_PROMPT = f"""
                    Convert the value from {source_unit} to {destination_unit} for 
                    this value: {value}
                
                    Respond with JSON in exactly this format: {{"result": <converted_value>}}
                    Do not include any other text or formatting.
                    """
        print(f"Converting {value} from {source_unit} to {destination_unit}...")
        response = PostProcess.client.responses.parse(
            model=POSTPROCESSING_MODEL,
            input=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {
                    "role": "user",
                    "content": USER_PROMPT,
                },
            ],
            text_format=PostProcess.Converter,
        )
        result = json.loads(response.output[0].content[0].text)["result"]
        print(f"Converted {value} from {source_unit} to {destination_unit}: {result}")
        return str(result)

    @staticmethod
    def standardize_reference_range(data: Dict[str, Any]) -> Dict[str, Any]:
        """Standardize the reference range values in the biomarker data.
        Args:
            data (dict): The biomarker dictionary with min_value and max_value
        Returns:
            dict: The modified dictionary with standardized min_value and max_value
        """
        #ToDo: Implement the logic to standardize the reference range values based on conversion units and not the test result
        try:
            if float(data.get(DataKeys.TEST_RESULT)) == 0:
                # Avoid division by zero
                factor = None
            else:
                factor = float(data.get(DataKeys.RESULT_STANDARDIZED)) / float(
                data.get(DataKeys.TEST_RESULT)
                )  # floating point arithmetic tends to f up this calculation (think 69.0/0.069 = 999.9999999999999)
        except Exception as e:
            factor = None
    
        if factor is not None:
            if data.get(DataKeys.MIN_REFERENCE_RANGE) is not None:
                data[DataKeys.STANDARDIZED_MIN_REFERENCE_RANGE] = (
                    data.get(DataKeys.MIN_REFERENCE_RANGE) * factor
                )
            if data.get(DataKeys.MAX_REFERENCE_RANGE) is not None:
                data[DataKeys.STANDARDIZED_MAX_REFERENCE_RANGE] = (
                    data.get(DataKeys.MAX_REFERENCE_RANGE) * factor
                )
        else:
            data[DataKeys.STANDARDIZED_MIN_REFERENCE_RANGE] = data.get(
                DataKeys.MIN_REFERENCE_RANGE
            )
            data[DataKeys.STANDARDIZED_MAX_REFERENCE_RANGE] = data.get(
                DataKeys.MAX_REFERENCE_RANGE
            )

        return data


if __name__ == "__main__":
    # Example usage
    with open("biomarker_enrichment_data.json", "r") as file:
        dummy_biomarkers = json.load(file)

    dummy_biomarkers2 = {
        "biomarkers": [
            {
                "n1_id": "3dd4e333-4cc9-4064-93d6-eaf4a36ae945",
                "test_date": 1747872000000,
                "test_result": "0.069",
                "custom_reference_range": "0.062-0.082",
                "unit": "g/ml",
                "canonical_name": "Protein [Mass/volume] in Serum or Plasma",
                "sample_source": "SERUM",
                "method": "None",
                "test_name": "Total Protein",
                "context": "CLINICAL BIOCHEMISTRY > LIVER FUNCTION TEST (LFT)",
                "health_areas": ["hematology"],
                "health_area_count": 1,
            }
        ]
    }

    for data in dummy_biomarkers2["biomarkers"]:
        print("Original data:\n", data)
        print(" ..................")
        enriched_data = PostProcess.enrich_data(data)
        print("Enriched data:\n", enriched_data)
        print(" ________________________________________________________")
    test_cases = [
        "15-20mg",
        ">5g/dl",
        "<10ml",
        ">=15.5mg/dl",
        "<=25units/ml",
        "10 - 20 g",
        "5.5mg",
        "> 10.2 g/dl",
        "< 15.8 ml",
        "10.5 - 25.2 mg/dl",
        "100mg/dl",
    ]

    for test in test_cases:
        try:
            result = PostProcess._extract_range_pattern(test)
            print(f"'{test}' -> '{result}'")
        except ValueError as e:
            print(f"'{test}' -> ERROR: {e}")
